<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-form-item prop="p1" label="1、患者是否签署知情同意书？">
                <el-radio-group v-model="form.p1">
                    <el-radio label="是" />
                    <el-radio label="否" />
                </el-radio-group>
            </el-form-item>
            <el-form-item prop="p1" label="2、知情同意书">
                <template v-slot:label>
                    <span>2、知情同意书</span>
                    <el-button type="primary" size="mini" style="margin-left: 10px;"  @click="handleShow">查看</el-button>
                </template>
                <el-button type="primary" plain size="mini" @click="handleAdd">点击上传知情同意书</el-button>
            </el-form-item>
        </el-form>
        <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
            <el-upload ref="upload" :limit="1" accept=".jpg, .png, .gif" :auto-upload="false" drag
                        :headers="upload.headers" :action="upload.url" :data="upload.data" :disabled="upload.isUploading"
                        :on-change="handleFileChange"
                        :on-progress="handleFileUploadProgress"
                        :on-success="handleFileSuccess">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                将文件拖到此处，或 <em>点击上传</em>
                </div>
                <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入 jpg、png、gif 格式文件！</div>
            </el-upload>
            <template slot="footer">
              <div class="dialog-footer">
                  <el-button type="primary" @click="submitFileForm">确 定</el-button>
                  <el-button @click="upload.open = false">取 消</el-button>
              </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import {getAccessToken} from "@/utils/auth";
export default {
    name: "AN0001",
    props: {
        formData: String,
        fileList: [],
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {
                p1: undefined,
            },
            // 用户导入参数
            upload: {
                open: false, // 是否显示弹出层
                title: "", // 弹出层标题
                isUploading: false, // 是否禁用上传
                url: process.env.VUE_APP_BASE_API + "/admin-api/infra/file/upload", // 请求地址
                headers: {Authorization: "Bearer " + getAccessToken()}, // 设置上传的请求头部
                data: {} // 上传的额外数据，用于文件名
            },
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "患者是否签署知情同意书必须选择",
                        trigger: "blur",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: '请上传知情同意书',
                        trigger: 'blur'
                    }
                ]
            },
        };
    },
    methods: {
        handleShow() {
            console.log('查看：', this.fileList)
        },
        handleAdd() {
            this.upload.open = true;
            this.upload.title = "上传文件";
        },
        getFileList(){
            return this.fileList;
        },
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        /** 处理上传的文件发生变化 */
        handleFileChange(file, fileList) {

        },
        /** 处理文件上传中 */
        handleFileUploadProgress(event, file, fileList) {
            this.upload.isUploading = true; // 禁止修改
        },
        /** 发起文件上传 */
        submitFileForm() {
            this.$refs.upload.submit();
        },
        /** 文件上传成功处理 */
        handleFileSuccess(response, file, fileList) {
            // 清理
            this.upload.open = false;
            this.upload.isUploading = false;
            this.$refs.upload.clearFiles();
            // 提示成功，并刷新
            this.$modal.msgSuccess("上传成功");
            // 将上传成功的文件信息添加到临时列表中
            const uploadedFile = {
                fileName: file.name,
                filePath: response.data
            };
            this.form.p2 = file.name
            this.fileList.unshift(uploadedFile); // 插入到列表头部
        },
        // 下载文件
        downloadFile(row) {
            window.open(row.filePath, "_blank");
        },
        deleteFile(row) {
            this.fileList = this.fileList.filter((item) => item.name !== row.name);
            delete this.form.p2
        },
    },
};
</script>
<style scoped>
/* 自定义选中后的背景颜色 */
/deep/.el-radio.is-bordered.is-checked {
  background-color: springgreen !important; /* 设置选中后的背景色为红色 */
}

/* 自定义未选中的样式（可选） */
/deep/.el-radio.is-bordered {
  background-color: #fff; /* 默认背景色 */
  border-color: #dcdfe6; /* 默认边框颜色 */
}

/deep/.el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px!important;
  border-radius: 3px;
  height: 28px;
}

/deep/.el-radio__input {
  display: none!important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

/deep/.el-radio__label {
  padding-left: 0px;
}

/deep/.el-form-item__label {
  text-align: right;
  vertical-align: middle;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

/deep/.el-radio-group {
  font-size: 0;
  margin-top: 7px;
}

/deep/.el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}
</style>
