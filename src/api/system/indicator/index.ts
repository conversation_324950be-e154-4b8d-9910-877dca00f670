import request from '@/utils/request'

// 创建指标
export function createIndicator(data) {
  return request({
    url: '/system/indicator/create',
    method: 'post',
    data: data
  })
}

// 更新指标
export function updateIndicator(data) {
  return request({
    url: '/system/indicator/update',
    method: 'put',
    data: data
  })
}

// 删除指标
export function deleteIndicator(id) {
  return request({
    url: '/system/indicator/delete?id=' + id,
    method: 'delete'
  })
}

// 获得指标
export function getIndicator(id) {
  return request({
    url: '/system/indicator/get?id=' + id,
    method: 'get'
  })
}

// 获得指标列表
export function getIndicatorList(params) {
  return request({
    url: '/system/indicator/list',
    method: 'get',
    params
  })
}
// 导出指标 Excel
export function exportIndicatorExcel(params) {
  return request({
    url: '/system/indicator/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}


// 获取部门精简信息列表
export function listSimpleIndicators() {
  return request({
    url: '/system/indicator/list-all-simple',
    method: 'get'
  })
}
