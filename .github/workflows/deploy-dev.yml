name: Deploy to DEV

on:
  push:
    branches:
      - develop
  workflow_dispatch:
    inputs:
      branch:
        description: "Branch to deploy"
        required: true
        default: "develop"

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 22

      - name: Install dependencies
        run: |
          npm run init:submodule
          npm cache clean --force
          rm -rf node_modules
          yarn install --production=false
          mv ./nginx.dev.conf ./nginx.conf
          npm run build:dev

      - name: Build Docker image
        run: |
          docker build -t iris-web .

      - name: Log in to aliyuncs
        run: |
          echo "${{ secrets.DOCKER_PASSWORD }}" | docker login --username=petezhang --password-stdin crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com

      - name: Push Docker image to aliyuncs
        run: |
          docker tag iris-web crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com/iris-platform/iris-web-dev:${{ github.run_number }}
          docker push crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com/iris-platform/iris-web-dev:${{ github.run_number }}

      - name: Deploy to ECS
        uses: appleboy/ssh-action@master
        with:
          host: *************
          username: root
          password: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            sshpass -p "${{ secrets.TENCENT_SSH_KEY }}" ssh -o StrictHostKeyChecking=no deploy_user@*********** << 'EOF'
              echo "${{ secrets.DOCKER_PASSWORD }}" | docker login --username=petezhang --password-stdin crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com
              docker pull crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com/iris-platform/iris-web-dev:${{ github.run_number }}
              docker stop iris-web-dev || true
              docker rm iris-web-dev || true
              docker run -d --name iris-web-dev -p 8081:8081 crpi-9dfdvx267ioh5678.cn-hangzhou.personal.cr.aliyuncs.com/iris-platform/iris-web-dev:${{ github.run_number }}
              docker image prune -af \
              --filter until=1h \
              --filter label!="protected=true"
            EOF
