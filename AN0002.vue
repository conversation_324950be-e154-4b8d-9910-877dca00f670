<template>
    <div>
        <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-position="top"
            size="mini"
        >
            <el-form-item label="1、身高" prop="p1">
                <el-input
                    @input="handleInputP1"
                    @change="calBmi"
                    v-model="form.p1"
                    style="width: 250px"
                    ><template slot="append">cm</template></el-input
                >
            </el-form-item>

            <el-form-item label="2、体重" prop="p2">
                <el-input
                    @input="handleInputP2"
                    @change="calBmi"
                    v-model="form.p2"
                    style="width: 250px"
                    ><template slot="append">kg</template></el-input
                >
            </el-form-item>
            <el-form-item label="3、BMI">
                <el-input
                    :disabled="true"
                    v-model="form.p3"
                    style="width: 250px"
                    ><template slot="append">kg/m²</template></el-input
                >
            </el-form-item>
            <el-form-item label="4、腰围">
                <el-input
                    v-model="form.p4"
                    style="width: 250px"
                    @input="handleNumberInput($event, 'p4')"
                    ><template slot="append">cm</template></el-input
                >
            </el-form-item>
            <el-form-item label="5、臀围">
                <el-input
                    v-model="form.p5"
                    style="width: 250px"
                    @input="handleNumberInput($event, 'p5')"
                    ><template slot="append">cm</template></el-input
                >
            </el-form-item>
            <el-form-item label="6、腰臀比">
                <el-input
                    v-model="form.p6"
                    style="width: 250px"
                    :disabled="true"
                    ></el-input
                >
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
export default {
    name: "AN0002",
    props: {
        formData: String,
    },
    components: {},
    created() {
        if (this.formData != null && this.formData != "") {
            this.form = JSON.parse(this.formData);
        }
    },
    data() {
        return {
            form: {},
            // 表单校验
            rules: {
                p1: [
                    {
                        required: true,
                        message: "身高必须输入",
                        trigger: "blur",
                    },
                ],
                p2: [
                    {
                        required: true,
                        message: "体重必须输入",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        /** 表单值 */
        getData() {
            return this.form;
        },
        validateData() {
            let rs = true;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    rs = false;
                }
            });
            return rs;
        },
        handleInputP1(value) {
            this.form.p1 = value.toString().replace(/\D/g, "").slice(0, 3);
            this.calBmi();
        },
        handleInputP2(value) {
            this.form.p2 = value = value
                .replace(/(\.\d{2})\d*/, "$1")
                .slice(0, 6); // 最多保留2位小数
            this.calBmi();
        },
        calBmi() {
            if (this.form.p1 != null && this.form.p2 != null) {
                this.form.p3 = (
                    this.form.p2 /
                    (((this.form.p1 / 100) * this.form.p1) / 100)
                )
                    .toString()
                    .replace(/(\.\d{2})\d*/, "$1");
            }
        },
        calYTB() {
            if (this.form.p4 != null && this.form.p5 != null) {
                this.form.p6 = (this.form.p4 / this.form.p5).toFixed(2);
            }
        },
        handleNumberInput(event, field) {
        if (field == 'p6') {
            let value = event.replace(/[^0-9.]/g, '');

            // 处理多个小数点的情况
            const decimalParts = value.split('.');
            if (decimalParts.length > 2) {
            value = decimalParts[0] + '.' + decimalParts.slice(1).join('');
            }

            // 限制整数部分为3位
            if (decimalParts[0].length > 2) {
            value = decimalParts[0].substring(0, 2) + (decimalParts[1] ? '.' + decimalParts[1] : '');
            }

            // 限制小数部分为2位
            if (decimalParts.length > 1 && decimalParts[1].length > 2) {
            value = decimalParts[0] + '.' + decimalParts[1].substring(0, 2);
            }
            this.form.p5 = value;
        } else {
            // 移除非数字字符
            let num = event.replace(/\D/g, '')
            
            // 限制长度为3位
            if (num.length > 3) {
            num = num.slice(0, 3)
            }
            // 更新绑定值
            this.form[field] = num
            this.calYTB()
        }


        },
    },
};
</script>
